/************************************************************/
/*    NAME: zhaoqinchao                                  */
/*    ORGN: HEU                                            */
/*    FILE: iElevator_HEU.cpp                              */
/*    DESC: 升降电机控制系统                                */
/*          与升降电机的CAN总线通信                          */
/*    DATE: 2024                                           */
/*    VERSION: 1.2.0                                       */
/*                                                          */
/*    MODIFICATION HISTORY:                                 */
/*    v1.2.0 (2024/12/XX) - zhaoqinchao                     */
/*      - 完善MotorError枚举，添加编码器错误、刹车电压过高、 */
/*        DRV驱动错误等错误码                               */
/*      - 集成BlueSocket重试连接功能，提高网络连接稳定性     */
/*      - 改进错误处理和资源管理，确保套接字正确关闭         */
/*      - 区分不同错误类型的错误码，增强系统诊断能力         */
/*      - 优化配置参数处理，支持默认值和告警机制             */
/*      - 协议错误码解析，实现实时电机故障告警              */
/************************************************************/

#include <iterator>
#include <cmath>
#include "MBUtils.h"
#include "iElevator_HEU.h"
using namespace std;
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/ip.h> /* superset of previous */

//---------------------------------------------------------
// 构造函数 - 初始化所有变量

iElevator_HEU::iElevator_HEU()
{
    m_nIterations = 0;
    m_dTimewarp = 1;
    SetAppFreq(10);   // 应用程序频率10Hz
    SetCommsFreq(10); // 通信频率10Hz

    // 初始化控制标志位分别为手动上升，手动下降，自动控制，GF模式
    m_bManualAscend = false;
    m_bManualDescend = false;
    m_bAutoControl = false;
    m_bGFMode = false;
    m_dTargetAngle = 0.0;

    // 初始化期望速度
    m_dDesiredSpeed = 0.0;

    // 初始化速度配置参数（默认值）
    m_dHighSpeed = 2500.0;

    // 初始化电流配置参数（默认值）
    m_dCurrentThreshold = 2.0; // 默认2.0A

    // 初始化角度配置参数（默认值）
    m_dMaxAscendAngle = 3240.0; // 9圈总角度
    m_dMinDescendAngle = 360.0; // 1圈总角度（预留安全余量）

    // 初始化主控状态为空
    m_sMainframeState = "";

    // 初始化导航和任务状态为空
    m_bNavigationStarted = false;
    m_sMissionStatus = "";

    // 当前深度初始化为0.7，期望深度为0
    m_dCurrentDepth = 0.7;
    m_dDesiredDepth = 1.0;

    // 初始化四个状态标志位分别为升到顶，降到底，在运动，卡在中间
    m_bAtTop = false;
    m_bAtBottom = false;
    m_bInMotion = false;
    m_bStuckInMiddle = false;

    // 初始化线程相关
    m_RecvThread = 0;
    m_bThreadRunning = false;

    // 初始化启动延迟控制
    // 启动延迟为60秒
    m_dStartupDelay = 60.0;
    // 启动时间
    m_dStartupTime = 0.0;
    // 启动延迟是否已执行
    m_bStartupDelayExecuted = false;

    // 初始化自动控制状态标志分别为导航启动后是否已下沉，任务结束后是否已上升，应急状态是否已上升
    m_bHasDescendedAfterNav = false;
    m_bHasAscendedAfterFinish = false;
    m_bHasAscendedInEmergency = false;
    // 上次的任务状态
    m_sLastMissionStatus = "";

    // 初始化GF模式升起状态跟踪
    m_bAscendInProgress = false;
    m_dAscendStartTime = 0.0;
    m_dAscendTimeout = 60.0; // 60秒超时

    m_dMotorTotalAngle = 0.0;
    m_uMotorRounds = 0;
    m_uMotorDegrees = 0;
    m_uMotorCurrent = 0;
    m_uMotorState = STATE_IDLE; // 初始化电机状态为IDLE
    m_uMotorError = ERROR_NONE; // 初始化电机错误状态为无错误
    // 初始化CAN报文计时控制
    m_bCanTimingEnabled = false;

    // 初始化卡住检测相关变量
    m_bDescendCommandSent = false;
    m_dDescendCommandTime = 0.0;
    m_uLastRounds = 0;
    m_bStuckDetected = false;
    m_bStuckDetectionCompleted = false;

    // 初始化60秒后自动上升卡住处理相关变量
    m_bAutoAscentStuckDetection = false;
    m_dAutoAscentStartTime = 0.0;
    m_uAutoAscentLastRounds = 0;
    m_bAutoAscentStuckHandling = false;
    m_bWaitingForTargetPosition = false;
    m_bWaitingForZeroComplete = false;
}

//---------------------------------------------------------
// 析构函数 - 安全关闭线程和网络连接
iElevator_HEU::~iElevator_HEU()
{
    // 停止接收线程
    m_bThreadRunning = false;
    // 刷新缓冲区并退出线程
    if (m_RecvThread != 0)
    {
        // 强制刷新所有缓冲区
        fflush(stdout);
        fflush(stderr);

        // 直接分离线程，让其自然退出
        pthread_detach(m_RecvThread);
        m_RecvThread = 0;
    }
        // 先关闭套接字，中断可能的阻塞接收操作
    m_RecvSock.Close();
    m_SendSock.Close();
}

//---------------------------------------------------------
// 处理MOOS消息
bool iElevator_HEU::OnNewMail(MOOSMSG_LIST &NewMail)
{
    MOOSMSG_LIST::iterator p;
    for (p = NewMail.begin(); p != NewMail.end(); p++)
    {
        CMOOSMsg &msg = *p;
        string key = msg.GetKey();
        // CONTROL_MSG格式处理消息

        if (key == "CONTROL_MSG")
        {
            // 接收并解析控制消息
            string control_msg = msg.GetString();
            // 调用TypeChoice函数解析控制消息
            TypeChoice(control_msg);
        }
        else if (key == "DESIRED_SPEED")
        {
            // 更新期望速度
            double old_speed = m_dDesiredSpeed;
            m_dDesiredSpeed = msg.GetDouble();

            // 如果速度发生变化且当前不是自动模式，自动切换到自动模式
            if (old_speed != m_dDesiredSpeed && !m_bAutoControl && !m_bGFMode)
            {
                m_bAutoControl = true;
            }
        }
        else if (key == "Mainframestate")
        {
            // 更新主控状态
            m_sMainframeState = msg.GetString();
            // 检测主控失联，自动升起
            if (m_sMainframeState == "The master has died")
            {
                m_bManualAscend = true;
                m_bManualDescend = false;
            }
        }
        else if (key == "CtrlMission_STATUS")
        {
            // 更新任务状态
            m_sMissionStatus = msg.GetString();
        }
        else if (key == "Depth")
        {
            // 更新当前深度
            m_dCurrentDepth = msg.GetDouble();
        }
        else if (key == "DESIRED_DEPTH")
        {
            // 更新期望深度
            m_dDesiredDepth = msg.GetDouble();
        }
    }

    return true;
}

//---------------------------------------------------------
// 连接到MOOS服务器后执行
bool iElevator_HEU::OnConnectToServer()
{
    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// 周期性执行函数，处理控制逻辑
bool iElevator_HEU::Iterate()
{
    m_nIterations++;

    // 1. 检查60秒启动延迟逻辑（只有启用CAN计时后才开始
    static bool s_bAutoStartupExecuted = false; // 静态变量，记录是否已执行过自动启动
    // 50-60秒逻辑能够执行
    if (!s_bAutoStartupExecuted && m_bCanTimingEnabled && m_dStartupTime > 0)
    {
        // 获取当前时间
        double currentTime = MOOSTime();
        // 计算启动时间差
        double elapsedTime = currentTime - m_dStartupTime;
        // 检查时间窗口
        if (elapsedTime >= 50 && elapsedTime < 60)
        {
            MOOSTrace("调试：进入50-60秒检测窗口，经过时间=%.1f秒\n", elapsedTime);
            // 50s-60s发送下降到底端的指令
            if (!m_bStuckDetectionCompleted)
            {
                if (!m_bDescendCommandSent)
                {
                    // 首次发送下降指令，记录时间和初始位置
                    m_bManualDescend = true;
                    m_bManualAscend = false;
                    m_bDescendCommandSent = true;
                    m_dDescendCommandTime = currentTime;
                    m_uLastRounds = m_uMotorRounds; // 记录初始圈数
                    m_bStuckDetected = false;
                }
                else if (!m_bStuckDetected)
                {
                    // 检测卡住条件：在发送指令后的3秒内进行检测
                    double detectionTime = currentTime - m_dDescendCommandTime;
                    if (detectionTime <= 3.0)
                    {
                        // 电流检测：使用新的电流数据，转换为安培
                        double current_amperes = m_uMotorCurrent / 100.0;
                        bool current_exceeded = (current_amperes > m_dCurrentThreshold);

                        // 位置变化检测：圈数是否保持不变
                        bool position_unchanged = (m_uMotorRounds == m_uLastRounds);

                        // 确保不在底部才进行卡住判断
                        bool not_at_bottom = !m_bAtBottom;



                        // 判断是否卡住：未到底部且(电流超标或位置不变)
                        if (not_at_bottom && (current_exceeded || position_unchanged))
                        {
                            // 发送停转保持当前位置指令
                            SendHoldPositionCommand(m_uMotorRounds, m_uMotorDegrees, 0x01);

                            // 发送清零指令
                            SendZeroCommand();

                            // 标记卡住已处理和检测完成
                            m_bStuckDetected = true;
                            m_bStuckDetectionCompleted = true;
                        }
                        else
                        {
                            // 更新上次圈数记录，用于下次比较
                            m_uLastRounds = m_uMotorRounds;
                        }
                    }
                    else
                    {
                        m_bStuckDetected = true;           // 标记检测完成，避免重复检测
                        m_bStuckDetectionCompleted = true; // 标记整个检测流程完成

                        // 未检测到卡住，重置启动延迟标志，允许60秒后的正常自动升起逻辑
                        m_bStartupDelayExecuted = false;
                    }
                }
            }
        }
        // 如果启动时间差大于等于启动延迟，则切换到自动模式并执行一次上升
        if (elapsedTime >= m_dStartupDelay)
        {
            // 60秒后切换到自动模式，除非GF模式或高速模式
            if (!m_bGFMode && !IsHighSpeedMode())
            {
                // 切换到自动模式
                m_bAutoControl = true;
                m_bGFMode = false;
                // 设置手动上升标志位为true，手动下降标志位为false，启动延迟标志位为true
                m_bManualAscend = true;
                m_bManualDescend = false;
                s_bAutoStartupExecuted = true;  // 标记自动启动已执行
                m_bStartupDelayExecuted = true; // 保持原有逻辑

                // 启动60秒后自动上升卡住检测
                m_bAutoAscentStuckDetection = true;
                m_dAutoAscentStartTime = currentTime;
                m_uAutoAscentLastRounds = m_uMotorRounds;
                m_bAutoAscentStuckHandling = false;
            }
            else if (m_bGFMode)
            {
                s_bAutoStartupExecuted = true;  // 标记自动启动已执行
                m_bStartupDelayExecuted = true; // 保持原有逻辑
            }
            else if (IsHighSpeedMode())
            {
                s_bAutoStartupExecuted = true;  // 标记自动启动已执行
                m_bStartupDelayExecuted = true; // 保持原有逻辑
            }
        }
    }

    // 2. 60秒后自动上升卡住检测和处理逻辑
    if (m_bAutoAscentStuckDetection && !m_bAutoAscentStuckHandling)
    {
        double currentTime = MOOSTime();
        double detectionTime = currentTime - m_dAutoAscentStartTime;

        // 检测卡住条件：电流超标或位置不变（检测3秒）
        if (detectionTime <= 3.0 && !m_bAtTop)
        {
            // 电流检测：使用新的电流数据，转换为安培
            double current_amperes = m_uMotorCurrent / 100.0;
            bool current_exceeded = (current_amperes > m_dCurrentThreshold);

            // 位置变化检测：圈数是否保持不变
            bool position_unchanged = (m_uMotorRounds == m_uAutoAscentLastRounds);

            // 判断是否卡住：未到顶部且(电流超标或位置不变)
            if (!m_bAtTop && (current_exceeded || position_unchanged))
            {

                // 计算目标位置：当前位置 - 最大上升角度
                double current_angle = ConvertComponentsToAngle(m_uMotorRounds, m_uMotorDegrees);
                double target_angle = current_angle - m_dMaxAscendAngle;

                // 确保目标角度不小于最小下降角度
                if (target_angle < m_dMinDescendAngle)
                {
                    target_angle = m_dMinDescendAngle;
                }

                AngleComponents target_components = ConvertAngleToComponents(target_angle);

                // 发送目标位置控制指令
                SendPositionCommand(target_components.rounds, target_components.degrees, 0x01);

                // 设置处理状态
                m_bAutoAscentStuckHandling = true;
                m_bWaitingForTargetPosition = true;
                m_bAutoAscentStuckDetection = false; // 停止检测
            }
            else
            {
                // 更新上次圈数记录
                m_uAutoAscentLastRounds = m_uMotorRounds;
            }
        }
        else if (detectionTime > 3.0)
        {
            m_bAutoAscentStuckDetection = false; // 停止检测
        }
        else if (m_bAtTop)
        {
            m_bAutoAscentStuckDetection = false; // 停止检测
        }
    }

    // 3. 检测手动控制标志位
    // 如果手动上升标志位为true，则执行手动上升控制
    if (m_bManualAscend)
    {
        MOOSTrace("执行手动上升控制\n");
        ExecuteAscendControl();
        m_bManualAscend = false; // 执行后重置标志位
    }

    // 如果手动下降标志位为true，则执行手动下降控制
    if (m_bManualDescend)
    {
        MOOSTrace("执行手动下降控制\n");
        ExecuteDescendControl();
        m_bManualDescend = false; // 执行后重置标志位

        // 手动下降时标记启动延迟已执行，防止60秒自动抬起逻辑冲突
        if (!m_bStartupDelayExecuted)
        {
            m_bStartupDelayExecuted = true;
            MOOSTrace("手动下降执行，标记启动延迟已执行，停止60秒自动抬起\n");
        }
    }

    // 3. 自动控制逻辑：只有启用时才运行
    if (m_bAutoControl)
    {
        ExecuteAutoControl();
    }

    // 4. 检测GF模式标志位
    // 如果GF模式标志位为true，则执行GF模式
    if (m_bGFMode)
    {
        ExecuteGFMode();
    }

    // 发布统一的LIFT状态（优先级：MOTION > TOP > BOTTOM > MIDDLE）
    std::string lift_status;
    if (m_bInMotion)
    {
        lift_status = "MOTION";
    }
    else if (m_bAtTop)
    {
        lift_status = "TOP";
    }
    else if (m_bAtBottom)
    {
        lift_status = "BOTTOM";
    }
    else if (m_bStuckInMiddle)
    {
        lift_status = "MIDDLE";
    }
    else
    {
        lift_status = " "; // 默认状态
    }

    Notify("LIFT_Status", lift_status);

    // 持续发布电机状态变量
    Notify("MOTOR_TOTAL_ANGLE", m_dMotorTotalAngle);
    Notify("MOTOR_ROUNDS", (double)m_uMotorRounds);
    Notify("MOTOR_DEGREES", (double)m_uMotorDegrees);
    Notify("MOTOR_CURRENT", (double)m_uMotorCurrent);
    Notify("MOTOR_STATE", (double)m_uMotorState);
    Notify("MOTOR_ERROR", (double)m_uMotorError);

    // 在Iterate中检查GF模式是否应该退出（只有真正完成上升且到达顶部才退出）
    if (m_bGFMode && m_bAscendInProgress && m_bAtTop)
    {
        // GF模式执行完成且已到达顶部，直接切换到自动模式
        MOOSTrace("GF模式：检测到已到达顶部，立即退出GF模式，切换到自动控制模式\n");
        m_bGFMode = false;
        m_bAutoControl = true;
    }

    return true;
}

//---------------------------------------------------------
// 应用启动时执行，加载配置
bool iElevator_HEU::OnStartUp()
{
    // 从配置文件获取网络参数
    string sRecvIP;
    if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
    {
        MOOSTrace("未指定RecvIP参数，使用默认值0.0.0.0\n");
        sRecvIP = "0.0.0.0";
    }

    int iRecvPort;
    if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
    {
        MOOSTrace("错误: 必须指定接收端口RecvPort\n");
    }

    string sDestIP;
    if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP))
    {
        MOOSTrace("错误: 必须指定目标IP地址DestIP\n");
    }

    int iDestPort;
    if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
    {
        MOOSTrace("错误: 必须指定目标端口DestPort\n");
    }

    // 读取高速阈值配置参数，来自于配置文件
    m_MissionReader.GetConfigurationParam("HIGH_SPEED", m_dHighSpeed);

    // 读取电流阈值配置参数，来自于配置文件
    m_MissionReader.GetConfigurationParam("CURRENT_THRESHOLD", m_dCurrentThreshold);

    // 读取角度配置参数，最大上升角度和最小下降角度，来自于配置文件
    m_MissionReader.GetConfigurationParam("MAX_ASCEND_ANGLE", m_dMaxAscendAngle);
    m_MissionReader.GetConfigurationParam("MIN_DESCEND_ANGLE", m_dMinDescendAngle);

    // 读取启动延迟配置，来自于配置文件
    m_MissionReader.GetConfigurationParam("STARTUP_DELAY", m_dStartupDelay);

    // 读取重试配置参数，来自于配置文件
    int maxRetries = 5;
    int retryDelay = 2;
    m_MissionReader.GetConfigurationParam("MaxRetries", maxRetries);
    m_MissionReader.GetConfigurationParam("RetryDelay", retryDelay);

    // 记录程序启动时间，这边是记录程序启动时间，用于启动延迟逻辑
    m_dStartupTime = MOOSTime();

    MOOSTrace("速度配置: 高速=%.1fmm/s\n", m_dHighSpeed);
    MOOSTrace("电流配置: 卡住检测阈值=%.1fA\n", m_dCurrentThreshold);

    // 显示角度配置和自动计算的圈数
    AngleComponents max_components = ConvertAngleToComponents(m_dMaxAscendAngle);
    AngleComponents min_components = ConvertAngleToComponents(m_dMinDescendAngle);
    MOOSTrace("角度配置: 最大上升=%.1f度(%d圈+%d度), 最小下降=%.1f度(%d圈+%d度)\n",
              m_dMaxAscendAngle, max_components.rounds, max_components.degrees,
              m_dMinDescendAngle, min_components.rounds, min_components.degrees);
    MOOSTrace("启动延迟配置: %.1f秒后自动升起\n", m_dStartupDelay);

    // 初始化网络 - 使用重试机制
    if (!m_RecvSock.OpenSocketWithRetry(sRecvIP, iRecvPort, maxRetries, retryDelay))
    {
        // 这是系统级的告警，当MOOS配置文件读取失败时触发
        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=0;LEVEL=FATAL;NOTE=MOOS File Read Failed");
        MOOSTrace("错误: 无法打开接收套接字，已重试%d次，程序将继续运行\n", maxRetries);
    }

    // 先尝试正常绑定，失败后再重试
    if (!m_RecvSock.BindSocket())
    {
        MOOSTrace("首次绑定失败，开始重试...\n");
        if (!m_RecvSock.RebindSocket(maxRetries, retryDelay))
        {
            // 这是网络通信的告警，当网络套接字绑定失败时触发
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=1;LEVEL=FATAL;NOTE=Network Binding Failed");
            MOOSTrace("错误: 无法绑定接收套接字，已重试%d次，程序将继续运行\n", maxRetries);
            m_RecvSock.Close(); // 关闭已打开的套接字
        }
    }

    // 设置接收超时为1秒，防止线程阻塞
    if (!m_RecvSock.SetReceiveTimeout(1, 0))
    {
        MOOSTrace("警告: 无法设置接收套接字超时\n");
    }

    if (!m_SendSock.OpenSocketWithRetry(sDestIP, iDestPort, maxRetries, retryDelay))
    {
        // 这是通信级的告警，当网络通信超时时触发
        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=3;LEVEL=FATAL;NOTE=Network Communication Timeout");
        MOOSTrace("错误: 无法打开发送套接字，已重试%d次，程序将继续运行\n", maxRetries);
        m_RecvSock.Close(); // 关闭已打开的接收套接字
    }

    // 创建接收线程
    m_bThreadRunning = true;
    if (pthread_create(&m_RecvThread, NULL, RecvThreadWrapper, this) != 0)
    {
        // 这是系统级的告警，当接收线程创建失败时触发
        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=2;LEVEL=FATAL;NOTE=Thread Creation Failed");
        MOOSTrace("错误: 无法创建接收线程，程序将继续运行\n");
        m_bThreadRunning = false;
        // 清理已打开的套接字
        m_RecvSock.Close();
        m_SendSock.Close();
    }
    MOOSTrace("接收线程创建成功\n");

    m_dTimewarp = GetMOOSTimeWarp();
    RegisterVariables();

    return true;
}

//---------------------------------------------------------
// 注册MOOS变量
void iElevator_HEU::RegisterVariables()
{
    // 注册控制消息和速度变量，来自于其他模块
    Register("CONTROL_MSG", 0);
    Register("DESIRED_SPEED", 0);
    Register("Mainframestate", 0);

    // 注册任务状态变量，来自于其他模块
    Register("CtrlMission_STATUS", 0);

    // 注册深度变量，当前深度和期望深度，来自于其他模块
    Register("Depth", 0);
    Register("DESIRED_DEPTH", 0);
}

//---------------------------------------------------------
// Procedure: RecvThreadWrapper - 线程包装函数
void *iElevator_HEU::RecvThreadWrapper(void *arg)
{
    iElevator_HEU *pThis = static_cast<iElevator_HEU *>(arg);
    pThis->RecvFrame();
    return NULL;
}

//---------------------------------------------------------
// Procedure: RecvFrame - 从网络接收帧数据
void iElevator_HEU::RecvFrame()
{
    MOOSTrace("接收线程开始运行（使用recvfrom阻塞IO）\n");

    while (m_bThreadRunning)
    {
        // 使用BlueSocket的RecvBinary方法接收数据
        vector<uint8_t> Frame;
        int n = m_RecvSock.RecvBinary(Frame, FRAME_LEN);

        if (n <= 0)
        {
            // 检查线程是否应该退出
            if (!m_bThreadRunning)
            {
                MOOSTrace("接收线程：收到退出信号\n");
                break;
            }

            // 接收失败或超时，继续下一次循环
            continue;
        }

        // 成功接收到数据，解析帧数据
        ParseFrame(Frame);

        // 检查线程是否应该退出
        if (!m_bThreadRunning)
        {
            break;
        }
    }

    MOOSTrace("接收线程结束\n");
}

//---------------------------------------------------------
// Procedure: ParseFrame - 解析接收到的帧数据
void iElevator_HEU::ParseFrame(vector<uint8_t> Frame)
{
    // 如果帧长度不等于13字节，则返回
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("帧长度无效: %d\n", Frame.size());
        return;
    }
    // 解析13字节网转CAN帧数据，分别是帧头，CAN ID，控制模式，运动状态，当前电流，当前圈数，当前度数，错误状态
    uint8_t frame_header = Frame[0]; // 帧头 0x08
    uint32_t can_id = (Frame[1] << 24) | (Frame[2] << 16) |
                      (Frame[3] << 8) | Frame[4]; // CAN ID (32位)

    // 只处理来自执行器反馈ID(0x112)的数据
    if (can_id == 0x112)
    {
        // 收到第一个CAN报文，启用计时
        if (!m_bCanTimingEnabled)
        {
            m_bCanTimingEnabled = true;
            MOOSTrace("收到第一个CAN ID 0x112报文，启用MOOSTime计时\n");
        }

        uint8_t control_mode = Frame[5];                         // 控制模式
        uint8_t motion_state = Frame[6];                         // 运动状态
        uint16_t current_current = (Frame[7] << 8) | Frame[8];   // 当前电流（字节2-3在协议中，但在Frame中是7-8）
        uint8_t current_rounds = Frame[9];                       // 当前圈数
        uint16_t current_degrees = (Frame[10] << 8) | Frame[11]; // 当前度数
        uint8_t error_state = Frame[12];                         // 错误状态

        // 计算总角度
        double total_angle = ConvertComponentsToAngle(current_rounds, current_degrees);

        // 更新成员变量
        m_dMotorTotalAngle = total_angle;
        m_uMotorRounds = current_rounds;
        m_uMotorDegrees = current_degrees;
        m_uMotorCurrent = current_current;  // 更新为电流数据
        m_uMotorState = motion_state;
        m_uMotorError = error_state;

        // 处理电机错误码并发送告警
        ProcessMotorError(error_state);

        // 更新状态标志位
        UpdateElevatorStatus(motion_state, current_rounds, current_degrees, current_current, error_state);

        // 堵转保护逻辑：检测到堵转时的处理
        if (error_state == ERROR_STALL)
        {
            // 如果在GF模式且正在上升过程中，立即下降
            if (m_bGFMode && m_bAscendInProgress)
            {
                MOOSTrace("堵转保护：GF模式上升堵转，立即切换为下降\n");
                m_bManualDescend = true;
                m_bManualAscend = false;
                m_bAscendInProgress = false;
                MOOSTrace("GF模式：检测到堵转的执行，发送下降指令，这种情况下就别先让他上升了\n");
            }
            // 如果在其他模式下堵转，停止当前操作
            else if (motion_state != STATE_IDLE)
            {
                MOOSTrace("堵转保护：检测到堵转，发送保持位置命令停止运动\n");
                SendHoldPositionCommand(current_rounds, current_degrees, 0x01); // 使用下降电机ID停止
            }
        }

        // GF模式下的升起成功/失败检测
        if (m_bGFMode && m_bAscendInProgress)
        {
            // 获取目标角度的圈数和角度
            AngleComponents target = ConvertAngleToComponents(m_dMaxAscendAngle);

            // 判断是否达到目标位置（圈数一致，角度误差在5度以内）
            bool ascend_success = (current_rounds == target.rounds) &&
                                  (abs((int)current_degrees - (int)target.degrees) <= 5);

            if (ascend_success)
            {
                MOOSTrace("GF模式：升起成功！当前位置(%d圈+%d度)达到目标位置(%d圈+%d度)\n",
                          current_rounds, current_degrees, target.rounds, target.degrees);
                MOOSTrace("GF模式：退出GF模式，切换到自动控制模式\n");

                // 升起成功：退出GF模式，启用自动控制
                m_bGFMode = false;
                m_bAutoControl = true;
                m_bAscendInProgress = false;
                m_bManualAscend = false;
                m_bManualDescend = false;
            }
            else if (error_state != ERROR_NONE || m_bStuckInMiddle)
            {
                // 特别处理堵转情况
                if (error_state == ERROR_STALL)
                {
                    MOOSTrace("GF模式：检测到上升堵转！立即发送下降指令\n");
                }
                else
                {
                    MOOSTrace("GF模式：升起失败！检测到错误(错误码:%d)或卡在中间\n", error_state);
                }

                MOOSTrace("GF模式：发送下降指令，保持GF模式\n");

                // 升起失败：发送下降指令，保持GF模式
                m_bManualDescend = true;
                m_bManualAscend = false;
                m_bAscendInProgress = false;
                // m_bGFMode 保持为 true
            }
        }

        // MOTOR变量的发布已移至Iterate函数中，确保持续发布

        MOOSTrace("收到反馈帧(CAN_ID=0x112): 模式=0x%02X, 状态=%d, 电流=%d, 圈数=%d, 度数=%d, 总角度=%.1f°, 错误=%d\n",
                  control_mode, motion_state, current_current, current_rounds, current_degrees, total_angle, error_state);
    }
    else
    {
        MOOSTrace("收到非反馈ID的数据帧: CAN_ID=0x%08X，忽略处理\n", can_id);
    }
}

//---------------------------------------------------------
// Procedure: UpdateElevatorStatus - 更新升降机构状态
void iElevator_HEU::UpdateElevatorStatus(uint8_t motion_state, uint8_t rounds, uint16_t degrees, uint16_t current, uint8_t error)
{
    // 计算当前总角度
    double current_total_angle = ConvertComponentsToAngle(rounds, degrees);

    // 计算目标角度的组件
    AngleComponents max_target = ConvertAngleToComponents(m_dMaxAscendAngle);
    AngleComponents min_target = ConvertAngleToComponents(m_dMinDescendAngle);

    // 判断是否在顶部（接近最大上升角度）
    double max_total_angle = ConvertComponentsToAngle(max_target.rounds, max_target.degrees);
    m_bAtTop = (current_total_angle >= (max_total_angle - 10.0)); // 10度容差

    // 判断是否在底部（接近最小下降角度）
    double min_total_angle = ConvertComponentsToAngle(min_target.rounds, min_target.degrees);
    m_bAtBottom = (current_total_angle <= min_total_angle); // 精确判断，不加容差
    // 判断是否在运动（电流大于0或运动状态不是静止）
    m_bInMotion = (current > 0 || motion_state != STATE_IDLE); // STATE_IDLE=2=静止状态

    // 判断是否卡在中间（有错误且不在顶部也不在底部）
    m_bStuckInMiddle = (error != ERROR_NONE && !m_bAtTop && !m_bAtBottom);

    // 自动保持位置逻辑
    if (m_bAtTop && motion_state != STATE_IDLE)
    {
        // 到达顶部时，使用上升电机ID (0x01) 发送保持位置命令
        // SendHoldPositionCommand(rounds, degrees, 0x01);

        // 到达顶部后，立即将运动标志位设置为false
        m_bInMotion = false;
    }
    else if (m_bAtBottom && motion_state != STATE_IDLE)
    {
        // 到达底部时，使用下降电机ID (0x02) 发送保持位置命令
        SendHoldPositionCommand(rounds, degrees, 0x01);

        // 发送保持位置命令后，立即将运动标志位设置为false
        m_bInMotion = false;
    }
    // 卡住处理状态检查
    if (m_bAutoAscentStuckHandling)
    {
        if (m_bWaitingForTargetPosition && motion_state == STATE_IDLE)
        {
            MOOSTrace("已到达卡住处理目标位置，发送清零指令\n");
            SendZeroCommand();
            m_bWaitingForTargetPosition = false;
            m_bWaitingForZeroComplete = true;
        }
        else if (m_bWaitingForZeroComplete && rounds == 0 && degrees == 0)
        {
            MOOSTrace("清零完成，恢复自动模式继续上升到顶部\n");
            // 恢复自动上升模式
            m_bManualAscend = true;
            m_bManualDescend = false;
            m_bAutoControl = true;

            // 重置卡住处理状态
            m_bAutoAscentStuckHandling = false;
            m_bWaitingForZeroComplete = false;

            // 重新启动卡住检测
            m_bAutoAscentStuckDetection = true;
            m_dAutoAscentStartTime = MOOSTime();
            m_uAutoAscentLastRounds = rounds;

            MOOSTrace("卡住处理完成，重新启动自动上升和卡住检测\n");
        }
    }
}

//---------------------------------------------------------
// Procedure: ProcessMotorError - 处理电机错误码并发送告警
void iElevator_HEU::ProcessMotorError(uint8_t can_error_code)
{
    // 使用静态变量记录上次错误码，避免重复发送相同告警
    static uint8_t last_error_code = 0xFF;

    // 只有当错误码发生变化时才发送告警
    if (can_error_code != last_error_code)
    {
        switch (can_error_code)
        {
        case 0: // 无异常
            if (last_error_code != 0xFF && last_error_code != 0)
            {
                MOOSTrace("电机错误已恢复：从错误码%d恢复到正常状态\n", last_error_code);
            }
            break;

        case 1: // 过热
            // 这是电机硬件的告警，当电机温度过高时触发（对应CAN协议错误码1）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=4;LEVEL=FATAL;NOTE=Motor Overheating");
            MOOSTrace("发送电机过热告警 (CAN错误码:1)\n");
            break;

        case 2: // 过流
            // 这是电机硬件的告警，当电机电流过大时触发（对应CAN协议错误码2）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=5;LEVEL=FATAL;NOTE=Motor Overcurrent");
            MOOSTrace("发送电机过流告警 (CAN错误码:2)\n");
            break;

        case 3: // 电压过低
            // 这是电机硬件的告警，当电机电压过低时触发（对应CAN协议错误码3）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=6;LEVEL=WARN;NOTE=Motor Undervoltage");
            MOOSTrace("发送电机欠压告警 (CAN错误码:3)\n");
            break;

        case 4: // 编码器错误
            // 这是电机硬件的告警，当编码器出现错误时触发（对应CAN协议错误码4）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=7;LEVEL=FATAL;NOTE=Encoder Error");
            MOOSTrace("发送编码器错误告警 (CAN错误码:4)\n");
            break;

        case 5: // 堵转
            // 这是电机硬件的告警，当电机发生堵转时触发（对应CAN协议错误码5）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=8;LEVEL=FATAL;NOTE=Motor Stall Detected");
            MOOSTrace("发送电机堵转告警 (CAN错误码:5)\n");
            break;

        case 6: // 刹车电压过高
            // 这是电机硬件的告警，当刹车电压异常时触发（对应CAN协议错误码6）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=9;LEVEL=WARN;NOTE=Brake Voltage Error");
            MOOSTrace("发送刹车电压异常告警 (CAN错误码:6)\n");
            break;

        case 7: // DRV驱动错误
            // 这是电机硬件的告警，当驱动器发生故障时触发（对应CAN协议错误码7）
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=10;LEVEL=FATAL;NOTE=DRV Driver Error");
            MOOSTrace("发送驱动器故障告警 (CAN错误码:7)\n");
            break;

        default: // 未知错误码
            MOOSTrace("收到未知电机错误码: %d\n", can_error_code);
            break;
        }

        // 更新上次错误码
        last_error_code = can_error_code;
    }
}

//---------------------------------------------------------
// Procedure: SendFrame - 发送帧数据
void iElevator_HEU::SendFrame(vector<uint8_t> Frame)
{
    int result = m_SendSock.SendBinary(Frame);
    if (result < 0)
    {
        MOOSTrace("发送数据失败\n");
    }
    else
    {
        MOOSTrace("发送数据成功，长度: %d\n", Frame.size());
    }
}

//---------------------------------------------------------
// Procedure: TypeChoice - 解析控制消息类型
bool iElevator_HEU::TypeChoice(std::string param)
{
    // 解析升降电机控制消息格式:
    // 手动控制: MsgType=control;Act=function;Type=motion;Mode=Ascend/Descend;
    // 自动控制: MsgType=control;Act=function;Type=Autorise;Enable=yes/no;
    // GF模式: MsgType=control;Act=function;Type=GF;Enable=yes/no;
    // 停止升起(GF模式): MsgType=control;Act=function;Type=stop;Enable=no;
    // 导航启动: Type=function;Act=NavStart;
    // 高速航行启航: Type=motion;Act=Start;
    // 角度模式: MsgType=control;ACT=function;TYPE=motion;Mode=MotorSetAngleMode;
    // 界面清零指令：MsgType=control;ACT=function;TYPE=motion;Reset=yes;

    MOOSTrace("=== TypeChoice 开始解析升降电机命令 ===\n");
    MOOSTrace("输入命令: [%s]\n", param.c_str());

    // 这是副本
    std::string sStr = param;
    // 转换为大写
    MOOSToUpper(sStr);
    // 去除字符串两端的空格
    MOOSTrimWhiteSpace(sStr);

    // 保存结果
    std::string sOneParam;
    // 存储参数名称（键）
    std::string sParamName;
    // 存储参数值（值）
    std::string sParamVal;

    // 第一步：检查是否为简单格式命令（没有MsgType前缀）
    // 分割第一个；
    sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is first param
    // 分割第一个=
    sParamName = MOOSChomp(sOneParam, "=");
    // 拿到键值
    sParamVal = sOneParam;

    // 检查是否为简单格式命令 (Type=xxx;Act=xxx)
    if (sParamName == "TYPE")
    {
        std::string sTypeVal = sParamVal;

        // 解析Act参数
        sOneParam = MOOSChomp(sStr, ";"); // chomp the next ";" ,result is Act
        sParamName = MOOSChomp(sOneParam, "=");
        std::string sActVal = sOneParam;

        if (sParamName == "ACT")
        {
            // 检查导航启动命令 (Type=motion;Act=Start) - 修复为普通导航启动
            if (sTypeVal == "MOTION" && sActVal == "START")
            {
                MOOSTrace("检测到导航启动命令 (Type=motion;Act=Start)\n");
                // 设置导航启动标志
                m_bNavigationStarted = true;
                MOOSTrace("导航已启动\n");

                // 如果当前是自动模式，会在ExecuteAutoControl中处理下沉逻辑
                // 如果当前是GF模式，会在ExecuteGFMode中处理相应逻辑
                MOOSTrace("导航启动，将根据当前模式执行相应逻辑\n");
                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                return true;
            }
        }
        else if (sParamName == "ID" && sTypeVal == "CONFIG")
        {
            // 处理配置命令 (Type=CONFIG;ID=MOTOR_POS;VALUE=xxx)
            std::string sIdVal = sOneParam;

            // 解析VALUE参数
            sOneParam = MOOSChomp(sStr, ";");
            sParamName = MOOSChomp(sOneParam, "=");
            std::string sValueVal = sOneParam;

            if (sParamName == "VALUE" && sIdVal == "MOTOR_POS")
            {
                MOOSTrace("检测到电机位置配置命令\n");
                double target_angle = atof(sValueVal.c_str());
                MOOSTrace("设置目标角度: %.1f度\n", target_angle);

                // 设置目标角度并启动运动
                m_dTargetAngle = target_angle;
                m_bManualAscend = true;
                m_bManualDescend = false;

                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                return true;
            }
            else
            {
                MOOSTrace("错误：CONFIG命令格式错误，ID=%s，VALUE参数=%s\n", sIdVal.c_str(), sParamName.c_str());
                return false;
            }
        }

        MOOSTrace("错误：未识别的简单格式命令: Type=%s;Act=%s\n", sTypeVal.c_str(), sActVal.c_str());
        return false;
    }

    // 判断键是否为MSGTYPE（复杂格式命令）
    if (sParamName != "MSGTYPE")
    {
        MOOSTrace("错误：命令格式错误，第一个参数应为MSGTYPE或TYPE\n");
        return false;
    }

    // 键值为control
    if (sParamVal == "CONTROL")
    {
        // 第二步：解析ACT
        // 分割第一个；
        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is ACT
        // 分割第一个=
        sParamName = MOOSChomp(sOneParam, "=");
        // 拿到值
        sParamVal = sOneParam;

        // 判断第二个参数是否为ACT
        if (sParamName == "ACT")
        {
            // 保存Act的值
            std::string sActVal = sParamVal;

            if (sParamVal == "FUNCTION")
            {
                // 第三步：解析TYPE
                sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is TYPE
                sParamName = MOOSChomp(sOneParam, "=");
                std::string sTypeVal = sOneParam;

                if (sParamName == "TYPE")
                {
                    // 根据TYPE的值进行不同的处理
                    if (sTypeVal == "MOTION")
                    {
                        // 第四步：解析Mode参数
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Mode
                        std::string sModeParam = MOOSChomp(sOneParam, "=");
                        std::string sModeVal = sOneParam;

                        MOOSTrace("调试：motion类型参数解析 - 参数名=%s, 参数值=%s\n", sModeParam.c_str(), sModeVal.c_str());

                        if (sModeParam == "MODE")
                        {
                            if (sModeVal == "ASCEND")
                            {
                                MOOSTrace("设置手动控制上升标志\n");
                                m_bManualAscend = true;
                                m_bManualDescend = false; // 确保下降标志为false

                                // 自动切换到手动模式：禁用自动控制和GF模式
                                if (m_bAutoControl || m_bGFMode)
                                {
                                    MOOSTrace("检测到手动上升命令，自动切换到手动模式\n");
                                    m_bAutoControl = false;
                                    m_bGFMode = false;
                                }

                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else if (sModeVal == "DESCEND")
                            {
                                MOOSTrace("设置手动控制下降标志\n");
                                m_bManualDescend = true;
                                m_bManualAscend = false; // 确保上升标志为false

                                // 自动切换到手动模式：禁用自动控制和GF模式
                                if (m_bAutoControl || m_bGFMode)
                                {
                                    MOOSTrace("检测到手动下降命令，自动切换到手动模式\n");
                                    m_bAutoControl = false;
                                    m_bGFMode = false;
                                }

                                // 手动下降命令时标记启动延迟已执行，防止60秒自动抬起逻辑冲突
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bStartupDelayExecuted = true;
                                    MOOSTrace("收到手动下降命令，标记启动延迟已执行，停止60秒自动抬起\n");
                                }

                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else if (sModeVal == "MOTORSETANGLEMODE")
                            {
                                MOOSTrace("角度控制模式命令已忽略 - 系统默认使用角度控制模式\n");
                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("错误：未知的Mode类型: %s\n", sModeVal.c_str());
                            }
                        }
                        else if (sModeParam == "RESET")
                        {
                            if (sModeVal == "YES")
                            {
                                MOOSTrace("收到界面清零指令，发送清零命令\n");
                                // 调用清零指令函数
                                SendZeroCommand();
                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("错误：Reset参数应为YES，实际为: %s\n", sModeVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("错误：motion类型需要Mode或Reset参数，实际为: %s\n", sModeParam.c_str());
                        }
                    }
                    else if (sTypeVal == "AUTORISE")
                    {
                        // 第四步：解析Enable参数
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "YES")
                            {
                                MOOSTrace("设置自动控制升降标志为启动\n");
                                m_bAutoControl = true;
                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else if (sEnableVal == "NO")
                            {
                                MOOSTrace("设置自动控制升降标志为关闭\n");
                                m_bAutoControl = false;
                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("错误：Enable参数应为YES或NO，实际为: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("错误：Autorise类型需要Enable参数\n");
                        }
                    }
                    else if (sTypeVal == "GF")
                    {
                        // 第四步：解析Enable参数
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "YES")
                            {
                                MOOSTrace("启动GF模式，停止60秒自动升起\n");
                                m_bGFMode = true;
                                // 如果正在准备自动升起，立即停止
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bManualAscend = false;
                                    m_bManualDescend = false;
                                    MOOSTrace("GF模式已停止自动升起\n");
                                }
                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else if (sEnableVal == "NO")
                            {
                                MOOSTrace("关闭GF模式\n");
                                m_bGFMode = false;
                                // 检查是否可以升起：启动延迟已过 或者 (导航已启动且任务已结束)
                                bool can_ascend = false;

                                // 情况1：启动延迟时间已过且未执行过（只有启用CAN计时后才开始）
                                if (!m_bStartupDelayExecuted && m_bCanTimingEnabled && m_dStartupTime > 0)
                                {
                                    double currentTime = MOOSTime();
                                    double elapsedTime = currentTime - m_dStartupTime;
                                    if (elapsedTime >= m_dStartupDelay)
                                    {
                                        can_ascend = true;
                                        MOOSTrace("GF模式关闭，启动延迟已过（从第一个CAN报文开始计时）\n");
                                    }
                                }

                                // 情况2：任务结束或应急状态且期望速度=0
                                if ((m_sMissionStatus == "FINISH" || m_sMissionStatus == "EMERGENCY") &&
                                    m_dDesiredSpeed == 0.0)
                                {
                                    if (m_sMissionStatus == "FINISH")
                                    {
                                        // 任务结束：需要导航已启动且深度<0.5米且无深度任务
                                        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
                                        {
                                            can_ascend = true;
                                            MOOSTrace("GF模式关闭，任务结束且期望速度=0且条件满足\n");
                                        }
                                    }
                                    else if (m_sMissionStatus == "EMERGENCY")
                                    {
                                        // 应急状态：立即升起
                                        can_ascend = true;
                                        MOOSTrace("GF模式关闭，应急状态且期望速度=0\n");
                                    }
                                }

                                if (can_ascend)
                                {
                                    MOOSTrace("设置自动升起\n");
                                    m_bManualAscend = true;
                                    m_bManualDescend = false;
                                    m_bStartupDelayExecuted = true;
                                }

                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("错误：Enable参数应为YES或NO，实际为: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("错误：GF类型需要Enable参数\n");
                        }
                    }
                    else if (sTypeVal == "STOP")
                    {
                        // 第四步：解析Enable参数
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "NO")
                            {
                                MOOSTrace("收到停止升起命令，对应GF模式\n");
                                // 激活GF模式，停止60秒自动升起
                                m_bGFMode = true;
                                // 如果正在准备自动升起，立即停止
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bManualAscend = false;
                                    m_bManualDescend = false;
                                    MOOSTrace("GF模式已停止自动升起\n");
                                }
                                MOOSTrace("=== TypeChoice 解析成功 ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("错误：STOP类型的Enable参数应为NO，实际为: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("错误：STOP类型需要Enable参数\n");
                        }
                    }
                    else
                    {
                        MOOSTrace("错误：未知的Type类型: %s\n", sTypeVal.c_str());
                    }
                }
                else
                {
                    MOOSTrace("错误：第三个参数应为TYPE\n");
                }
            }
            else
            {
                MOOSTrace("错误：第二个参数应为ACT=FUNCTION\n");
            }
        }
        else
        {
            MOOSTrace("错误：MsgType应为CONTROL\n");
        }

        MOOSTrace("=== TypeChoice 解析失败：命令格式不正确 ===\n");
        return false;
    }

    // 默认返回false（不应该到达这里）
    MOOSTrace("=== TypeChoice 解析失败：未知错误 ===\n");
    return false;
}
//---------------------------------------------------------
// Procedure: ExecuteAscendControl - 执行上升控制（倒伏电机）
void iElevator_HEU::ExecuteAscendControl()
{
    MOOSTrace("开始执行倒伏上升控制\n");
    // 构建倒伏上升控制帧 (13字节网转CAN格式)
    vector<uint8_t> ascendFrame(FRAME_LEN, 0);

    // CAN帧头部分 (字节0-4) - 倒伏电机CAN ID = 0x01
    ascendFrame[0] = 0x08; // 固定帧头
    ascendFrame[1] = 0x00; // CAN ID最高字节 (0x01 >> 24)
    ascendFrame[2] = 0x00; // CAN ID高字节 (0x01 >> 16)
    ascendFrame[3] = 0x00; // CAN ID低字节高位 (0x01 >> 8)
    ascendFrame[4] = 0x01; // CAN ID低字节低位 (0x01 & 0xFF)

    // CAN数据部分 (字节5-12) - 按照倒伏协议格式
    // 字节5: 控制模式 BB=角度控制模式
    ascendFrame[5] = 0xBB;

    // 字节6: 备用字节
    ascendFrame[6] = 0x00;

    // 字节7-8: 速度 (位置环控制时为0)
    MOOSTrace("倒伏角度控制模式：速度设为0\n");
    ascendFrame[7] = 0x00; // 速度高位
    ascendFrame[8] = 0x00; // 速度低位

    // 字节9-10: 期望度数 (4000~0x7FFF对应0°到90°)
    // 倒伏上升：目标角度为最大上升角度，直接转换为倒伏协议数值（圈数为0）
    uint16_t target_value = ConvertAngleToFallValue(m_dMaxAscendAngle);
    ascendFrame[9] = (target_value >> 8) & 0xFF; // 期望度数高位
    ascendFrame[10] = target_value & 0xFF;       // 期望度数低位

    // 字节11-12: 备用字节
    ascendFrame[11] = 0x00;
    ascendFrame[12] = 0x00;

    SendFrame(ascendFrame);
    MOOSTrace("倒伏上升控制帧已发送(CAN ID=0x01): 模式=0x%02X, 目标角度=%.1f°, 协议值=0x%04X\n",
              ascendFrame[5], m_dMaxAscendAngle, target_value);
}

//---------------------------------------------------------
// Procedure: ExecuteDescendControl - 执行下降控制（倒伏电机）
void iElevator_HEU::ExecuteDescendControl()
{
    MOOSTrace("开始执行倒伏下降控制\n");

    // 构建倒伏下降控制帧 (13字节网转CAN格式)
    vector<uint8_t> descendFrame(FRAME_LEN, 0);

    // CAN帧头部分 (字节0-4) - 倒伏电机CAN ID = 0x01
    descendFrame[0] = 0x08; // 固定帧头
    descendFrame[1] = 0x00; // CAN ID最高字节 (0x01 >> 24)
    descendFrame[2] = 0x00; // CAN ID高字节 (0x01 >> 16)
    descendFrame[3] = 0x00; // CAN ID低字节高位 (0x01 >> 8)
    descendFrame[4] = 0x01; // CAN ID低字节低位 (0x01 & 0xFF)

    // CAN数据部分 (字节5-12) - 按照倒伏协议格式
    // 字节5: 控制模式 BB=角度控制模式
    descendFrame[5] = 0xBB;

    // 字节6: 备用字节
    descendFrame[6] = 0x00;

    // 字节7-8: 速度 (位置环控制时为0)
    MOOSTrace("倒伏角度控制模式：速度设为0\n");
    descendFrame[7] = 0x00; // 速度高位
    descendFrame[8] = 0x00; // 速度低位

    // 字节9-10: 期望度数 (4000~0x7FFF对应0°到90°)
    // 倒伏下降：目标角度为最小下降角度，直接转换为倒伏协议数值（圈数为0）
    uint16_t target_value = ConvertAngleToFallValue(m_dMinDescendAngle);
    descendFrame[9] = (target_value >> 8) & 0xFF; // 期望度数高位
    descendFrame[10] = target_value & 0xFF;       // 期望度数低位

    // 字节11-12: 备用字节
    descendFrame[11] = 0x00;
    descendFrame[12] = 0x00;

    SendFrame(descendFrame);
    MOOSTrace("倒伏下降控制帧已发送(CAN ID=0x01): 模式=0x%02X, 目标角度=%.1f°, 协议值=0x%04X\n",
              descendFrame[5], m_dMinDescendAngle, target_value);
}

//---------------------------------------------------------
// Procedure: ExecuteAutoControl - 执行自动控制
void iElevator_HEU::ExecuteAutoControl()
{
    // 自动控制逻辑，处理导航和任务相关的自动升降场景

    // 1. 检查启航条件：导航启动后下沉
    if (m_bNavigationStarted && !m_bHasDescendedAfterNav)
    {
        MOOSTrace("检测到导航已启动，执行下沉操作\n");
        m_bManualDescend = true;
        m_bManualAscend = false;
        m_bHasDescendedAfterNav = true;

        // 导航启动下沉时标记启动延迟已执行，系统进入正常任务流程
        if (!m_bStartupDelayExecuted)
        {
            m_bStartupDelayExecuted = true;
            MOOSTrace("导航启动下沉，标记启动延迟已执行，系统进入正常任务流程\n");
        }
    }

    // 2. 检查任务结束条件：CtrlMission_STATUS = "FINISH" 时上升（除非GF模式或高速模式）
    MOOSTrace("自动控制调试：任务状态=%s, GF模式=%s, 高速模式=%s\n",
              m_sMissionStatus.c_str(), m_bGFMode ? "是" : "否", IsHighSpeedMode() ? "是" : "否");

    if (m_sMissionStatus == "FINISH" && !m_bGFMode && !IsHighSpeedMode())
    {
        MOOSTrace("自动控制：进入任务结束检查逻辑\n");

        // 检查上升条件：任务结束且期望速度为0即可自动上升
        MOOSTrace("自动控制：检查上升条件 - 期望速度=%.1f\n", m_dDesiredSpeed);
        // 任务结束且期望速度为0
        if (m_dDesiredSpeed == 0.0)
        {
            MOOSTrace("自动控制：期望速度条件满足，检查标志位 - 已上升=%s, 手动上升=%s, 手动下降=%s\n",
                      m_bHasAscendedAfterFinish ? "是" : "否",
                      m_bManualAscend ? "是" : "否",
                      m_bManualDescend ? "是" : "否");

            // 移除 m_bHasAscendedAfterFinish 检查，允许每次FINISH都触发上升
            if (!m_bManualAscend && !m_bManualDescend)
            {
                MOOSTrace("任务结束，期望速度=%.1f，执行自动上升（允许重复触发）\n", m_dDesiredSpeed);
                m_bManualAscend = true;
                m_bManualDescend = false;
                // 注释掉：不再设置已上升标志，允许重复触发
                // m_bHasAscendedAfterFinish = true;
            }
            else
            {
                MOOSTrace("自动控制：当前正在运动，跳过上升指令\n");
            }
        }
        else
        {
            MOOSTrace("自动控制：期望速度条件不满足\n");
        }
    }
    else
    {
        MOOSTrace("自动控制：不满足任务结束上升的基本条件\n");
    }
    if (m_sMissionStatus == "FINISH" && IsHighSpeedMode())
    {
        MOOSTrace("任务结束，但检测到高速模式，不执行自动上升\n");
    }

    // 4. 检查应急状态：立即上升（无需速度条件）
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("自动控制：检测到应急状态，期望速度=%.1f\n", m_dDesiredSpeed);

        // 检查任务状态是否刚刚变为EMERGENCY（重置标志）
        if (m_sLastMissionStatus != "EMERGENCY")
        {
            m_bHasAscendedInEmergency = false;
            MOOSTrace("应急状态刚刚开始，重置上升标志\n");
        }

        if (!m_bHasAscendedInEmergency && !m_bManualAscend && !m_bManualDescend)
        {
            MOOSTrace("检测到应急状态，立即执行上升（无视速度条件）\n");
            m_bManualAscend = true;
            m_bManualDescend = false;
            m_bHasAscendedInEmergency = true;
        }
        else
        {
            MOOSTrace("应急状态：已上升=%s, 手动上升=%s, 手动下降=%s\n",
                      m_bHasAscendedInEmergency ? "是" : "否",
                      m_bManualAscend ? "是" : "否",
                      m_bManualDescend ? "是" : "否");
        }
    }

    // 5. 基于速度的自动控制：根据DESIRED_SPEED决定升降
    // 应急状态下跳过速度控制，直接上升
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("自动控制：应急状态，跳过速度控制逻辑\n");
    }
    else
    {
        // 60秒后自动模式，根据航行速度决定升起/下降
        MOOSTrace("自动控制：速度控制 - 期望速度=%.0f mm/s, 高速阈值=%.0f mm/s\n",
                  m_dDesiredSpeed, m_dHighSpeed);

        // 避免频繁切换：只有在没有手动控制时才执行速度控制
        if (!m_bManualAscend && !m_bManualDescend)
    {
        // 期望的速度小于最高速，就升起来
        if (m_dDesiredSpeed < m_dHighSpeed && !m_bAtTop)
        {
            MOOSTrace("自动控制：低速(%.0f < %.0f)，执行上升\n",
                      m_dDesiredSpeed, m_dHighSpeed);
            m_bManualAscend = true;
            m_bManualDescend = false;
        }
        // 高速时下降（增加稳定性）
        else if (m_dDesiredSpeed >= m_dHighSpeed && !m_bAtBottom)
        {
            MOOSTrace("自动控制：高速(%.0f >= %.0f)，执行下降\n",
                      m_dDesiredSpeed, m_dHighSpeed);
            m_bManualDescend = true;
            m_bManualAscend = false;
        }
        else
        {
            MOOSTrace("自动控制：已在合适位置或到达极限位置\n");
        }
    }
        else
        {
            MOOSTrace("自动控制：当前有手动控制指令，跳过速度控制\n");
        }
    }

    // 更新上次任务状态
    m_sLastMissionStatus = m_sMissionStatus;
}

//---------------------------------------------------------
// Procedure: ExecuteGFMode - 执行GF模式
void iElevator_HEU::ExecuteGFMode()
{
    // 待启航后且任务结束，深度数据＜0.5米，60s升起成功之后，
    // 退出GF模式切换到自动模式，如果升起失败，发送下降指令，保持GF模式
    MOOSTrace("执行GF模式逻辑\n");

    // GF模式的核心逻辑：阻止舵机上升，直到收到任务结束或启航命令

    // 1. 阻止所有自动上升操作
    if (m_bManualAscend)
    {
        MOOSTrace("GF模式激活：阻止上升操作\n");
        m_bManualAscend = false; // 取消上升标志
    }

    // 2. GF模式主动下降逻辑：确保电梯降到底部（但要检查是否应该上升）
    // 首先检查是否满足上升条件
    bool should_ascend_now = false;

    // 检查任务结束上升条件
    if (m_sMissionStatus == "FINISH" && m_dDesiredSpeed == 0.0 && !IsHighSpeedMode())
    {
        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
        {
            should_ascend_now = true;
        }
    }

    // 检查应急状态上升条件（无需速度条件）
    if (m_sMissionStatus == "EMERGENCY")
    {
        should_ascend_now = true;
        MOOSTrace("GF模式：检测到应急状态，无视速度条件，准备上升\n");
    }

    // 只有在不需要上升时才执行下降逻辑
    if (!m_bAtBottom && !m_bManualDescend && !should_ascend_now)
    {
        MOOSTrace("GF模式激活：GF未在底部且无上升需求，执行下降到底部\n");
        m_bManualDescend = true; // 设置下降标志
        m_bManualAscend = false; // 确保上升标志为false
    }
    else if (should_ascend_now)
    {
        MOOSTrace("GF模式：检测到上升条件，跳过下降逻辑\n");
    }

    // 3. 检查是否应该退出GF模式并允许上升
    bool should_exit_gf_and_ascend = false;

    // 条件1：任务结束且满足上升条件（但不能是高速模式）
    if (m_sMissionStatus == "FINISH" && m_dDesiredSpeed == 0.0 && !IsHighSpeedMode())
    {
        MOOSTrace("GF模式：检查上升条件 - 任务状态=FINISH, 期望速度=%.1f, 高速模式=%s\n",
                  m_dDesiredSpeed, IsHighSpeedMode() ? "是" : "否");

        // 任务结束：需要导航已启动且深度<0.5米且无深度任务
        MOOSTrace("GF模式：详细条件检查 - 导航启动=%s, 当前深度=%.2f, 期望深度=%.2f\n",
                  m_bNavigationStarted ? "是" : "否", m_dCurrentDepth, m_dDesiredDepth);

        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
        {
            should_exit_gf_and_ascend = true;
            MOOSTrace("GF模式：所有条件满足，准备上升\n");
        }
        else
        {
            MOOSTrace("GF模式：条件不满足，继续等待\n");
        }
    }
    else if (m_sMissionStatus == "FINISH" && IsHighSpeedMode())
    {
        MOOSTrace("GF模式：任务结束但检测到高速模式，继续阻止上升\n");
    }

    // 条件4：应急状态 - 立即上升（无需速度条件）
    if (m_sMissionStatus == "EMERGENCY")
    {
        // 应急状态下立即上升，确保安全，无视速度条件
        MOOSTrace("GF模式：检测到应急状态，立即允许上升（无视速度=%.1f）\n", m_dDesiredSpeed);
        should_exit_gf_and_ascend = true; // 应急状态立即上升
    }

    // 条件5：启航命令（导航启动）
    if (m_bNavigationStarted && m_sMissionStatus != "FINISH")
    {
        // 只有在任务未结束时，启航才应该下降
        MOOSTrace("GF模式：检测到启航命令，任务未结束，应下降\n");
        should_exit_gf_and_ascend = false; // 阻止上升
    }

    // 如果满足退出GF模式并上升的条件
    MOOSTrace("GF模式：should_exit_gf_and_ascend = %s\n", should_exit_gf_and_ascend ? "true" : "false");
    if (should_exit_gf_and_ascend)
    {
        // 如果还没有开始升起操作，则启动升起并立即切换到自动模式
        if (!m_bAscendInProgress)
        {
            MOOSTrace("GF模式：条件满足，开始执行上升操作并切换到自动模式\n");

            // 立即退出GF模式，切换到自动模式
            m_bGFMode = false;
            m_bAutoControl = true;

            // 设置上升标志
            m_bManualAscend = true;     // 设置上升标志
            m_bManualDescend = false;   // 清除下降标志
            m_bAscendInProgress = true; // 标记升起操作开始

            // 标记启动延迟已执行（避免重复触发）
            if (!m_bStartupDelayExecuted)
            {
                m_bStartupDelayExecuted = true;
            }

            MOOSTrace("GF模式：已退出GF模式，切换到自动控制模式\n");
        }
        else
        {
            MOOSTrace("GF模式：上升操作已在进行中，m_bAscendInProgress = true\n");
        }
        // 升起操作正在进行中，升起成功和失败的检测在ParseFrame中处理
    }
    else
    {
        MOOSTrace("GF模式：继续阻止上升，等待任务结束或启航命令\n");
    }

    MOOSTrace("GF模式逻辑执行完成\n");
}

//---------------------------------------------------------
// Procedure: SendHoldPositionCommand - 发送保持位置命令
void iElevator_HEU::SendHoldPositionCommand(uint8_t current_rounds, uint16_t current_degrees, uint8_t motor_id)
{
    // 构建保持位置控制帧，使用当前圈数和角度作为目标位置
    vector<uint8_t> holdFrame(FRAME_LEN, 0);

    // CAN帧头部分 (字节0-4) - 使用指定的电机CAN ID
    holdFrame[0] = 0x08;     // 固定帧头
    holdFrame[1] = 0x00;     // CAN ID最高字节 (motor_id >> 24)
    holdFrame[2] = 0x00;     // CAN ID高字节 (motor_id >> 16)
    holdFrame[3] = 0x00;     // CAN ID低字节高位 (motor_id >> 8)
    holdFrame[4] = motor_id; // CAN ID低字节低位 (motor_id & 0xFF)

    // CAN数据部分 (字节5-12) - 倒伏协议格式
    holdFrame[5] = 0xBB;                           // 控制模式 = 角度控制
    holdFrame[6] = 0x00;                           // 备用字节
    holdFrame[7] = 0x00;                           // 速度高位 (位置环控制时为0)
    holdFrame[8] = 0x00;                           // 速度低位 (位置环控制时为0)

    // 倒伏协议：忽略圈数，直接使用度数值转换为倒伏协议值
    uint16_t fall_value = ConvertAngleToFallValue((double)current_degrees);

    holdFrame[9] = (fall_value >> 8) & 0xFF;       // 期望度数高位
    holdFrame[10] = fall_value & 0xFF;             // 期望度数低位
    holdFrame[11] = 0x00;                          // 备用字节
    holdFrame[12] = 0x00;                          // 备用字节

    SendFrame(holdFrame);
    MOOSTrace("倒伏保持位置控制帧已发送(CAN ID=0x%02X): 模式=0x%02X, 目标角度=%.1f°, 协议值=0x%04X\n",
              motor_id, holdFrame[5], (double)current_degrees, fall_value);
}

//---------------------------------------------------------
// Procedure: SendZeroCommand - 发送清零指令
void iElevator_HEU::SendZeroCommand()
{
    // 构建清零指令帧，使用CAN ID 0x7FF
    vector<uint8_t> zeroFrame(FRAME_LEN, 0);

    // CAN帧头部分 (字节0-4) - 清零指令CAN ID = 0x7FF
    zeroFrame[0] = 0x08; // 固定帧头
    zeroFrame[1] = 0x00; // CAN ID最高字节 (0x7FF >> 24)
    zeroFrame[2] = 0x00; // CAN ID高字节 (0x7FF >> 16)
    zeroFrame[3] = 0x07; // CAN ID低字节高位 (0x7FF >> 8)
    zeroFrame[4] = 0xFF; // CAN ID低字节低位 (0x7FF & 0xFF)

    // CAN数据部分 (字节5-12) - 根据协议文档设置清零指令
    zeroFrame[5] = 0x00;  // 舵机ID号
    zeroFrame[6] = 0x01;  // 控制器下发
    zeroFrame[7] = 0x00;  // 保留字节
    zeroFrame[8] = 0x03;  // 清零命令码
    zeroFrame[9] = 0x00;  // 保留字节
    zeroFrame[10] = 0x00; // 保留字节
    zeroFrame[11] = 0x00; // 保留字节
    zeroFrame[12] = 0x00; // 保留字节

    SendFrame(zeroFrame);
    MOOSTrace("清零指令已发送(CAN ID=0x7FF): 舵机ID=0x%02X, 控制码=0x%02X, 命令=0x%02X\n",
              zeroFrame[5], zeroFrame[6], zeroFrame[8]);
}

//---------------------------------------------------------
// Procedure: SendPositionCommand - 发送位置控制指令（倒伏协议）
void iElevator_HEU::SendPositionCommand(uint8_t target_rounds, uint16_t target_degrees, uint8_t motor_id)
{
    // 构建倒伏位置控制指令帧
    vector<uint8_t> positionFrame(FRAME_LEN, 0);

    // CAN帧头部分 (字节0-4) - 使用指定的电机ID
    positionFrame[0] = 0x08;     // 固定帧头
    positionFrame[1] = 0x00;     // CAN ID最高字节
    positionFrame[2] = 0x00;     // CAN ID高字节
    positionFrame[3] = 0x00;     // CAN ID低字节高位
    positionFrame[4] = motor_id; // CAN ID低字节低位

    // CAN数据部分 (字节5-12) - 倒伏协议位置控制指令
    positionFrame[5] = 0xBB;                          // 角度控制模式
    positionFrame[6] = 0x00;                          // 备用字节
    positionFrame[7] = 0x00;                          // 速度高位（位置环控制时为0）
    positionFrame[8] = 0x00;                          // 速度低位

    // 倒伏协议：忽略圈数，直接使用度数值转换为倒伏协议值
    // target_rounds在倒伏协议中无意义，直接使用target_degrees
    uint16_t fall_value = ConvertAngleToFallValue((double)target_degrees);

    positionFrame[9] = (fall_value >> 8) & 0xFF;      // 期望度数高位
    positionFrame[10] = fall_value & 0xFF;            // 期望度数低位
    positionFrame[11] = 0x00;                         // 备用字节
    positionFrame[12] = 0x00;                         // 备用字节

    SendFrame(positionFrame);
    MOOSTrace("倒伏位置控制指令已发送(CAN ID=0x%02X): 模式=0x%02X, 目标角度=%.1f°, 协议值=0x%04X\n",
              motor_id, positionFrame[5], (double)target_degrees, fall_value);
}

//---------------------------------------------------------
// Procedure: ConvertAngleToComponents - 将总角度转换为圈数+度数格式
AngleComponents iElevator_HEU::ConvertAngleToComponents(double total_angle)
{
    AngleComponents result;

    // 确保角度为正值
    if (total_angle < 0)
    {
        total_angle = 0;
        MOOSTrace("警告：角度值为负，已设为0\n");
    }

    // 计算完整圈数
    result.rounds = (uint8_t)(total_angle / 360.0);

    // 计算剩余度数
    double remaining_degrees = total_angle - (result.rounds * 360.0);
    result.degrees = (uint16_t)remaining_degrees;

    // 确保度数在0-359范围内
    if (result.degrees >= 360)
    {
        result.rounds += result.degrees / 360;
        result.degrees = result.degrees % 360;
    }
    // 这边如果是高速状态下的话就让他自己归零
    MOOSTrace("角度转换: %.1f° -> %d圈 + %d度\n",
              total_angle, result.rounds, result.degrees);

    return result;
}

//---------------------------------------------------------
// Procedure: ConvertAngleToFallValue - 将角度转换为倒伏协议值
uint16_t iElevator_HEU::ConvertAngleToFallValue(double angle)
{
    // 确保角度在0°-90°范围内
    if (angle < 0.0)
    {
        angle = 0.0;
        MOOSTrace("警告：倒伏角度值为负，已设为0°\n");
    }
    else if (angle > 90.0)
    {
        angle = 90.0;
        MOOSTrace("警告：倒伏角度值超过90°，已设为90°\n");
    }

    // 根据倒伏协议：4000~0x7FFF对应0°到90°
    // 计算公式：fall_value = 4000 + (angle / 90.0) * (0x7FFF - 4000)
    uint16_t fall_value = 4000 + (uint16_t)((angle / 90.0) * (0x7FFF - 4000));

    MOOSTrace("倒伏角度转换: %.1f° -> 0x%04X\n", angle, fall_value);

    return fall_value;
}

//---------------------------------------------------------
// Procedure: ConvertFallValueToAngle - 将倒伏协议值转换为角度
double iElevator_HEU::ConvertFallValueToAngle(uint16_t fall_value)
{
    // 确保协议值在4000-0x7FFF范围内
    if (fall_value < 4000)
    {
        fall_value = 4000;
        MOOSTrace("警告：倒伏协议值小于4000，已设为4000\n");
    }
    else if (fall_value > 0x7FFF)
    {
        fall_value = 0x7FFF;
        MOOSTrace("警告：倒伏协议值大于0x7FFF，已设为0x7FFF\n");
    }

    // 根据倒伏协议：4000~0x7FFF对应0°到90°
    // 计算公式：angle = ((fall_value - 4000) / (0x7FFF - 4000)) * 90.0
    double angle = ((double)(fall_value - 4000) / (double)(0x7FFF - 4000)) * 90.0;

    MOOSTrace("倒伏协议值转换: 0x%04X -> %.1f°\n", fall_value, angle);

    return angle;
}

/*
//---------------------------------------------------------
// Procedure: ConvertComponentsToAngle - 将圈数+度数转换为总角度
// 注释：倒伏协议不需要此函数，因为没有圈数概念，角度范围仅0°-90°
double iElevator_HEU::ConvertComponentsToAngle(uint8_t rounds, uint16_t degrees)
{
    // 确保度数在合理范围内
    if (degrees >= 360)
    {
        MOOSTrace("警告：度数值%d超过359，进行修正\n", degrees);
        // 这边是为什么要这么写的原因
        // rounds += degrees/360 → 增加完整圈数
        // degrees = degrees%360 → 保留剩余度数
        rounds += degrees / 360;
        degrees = degrees % 360;
    }
    // 总角度等于圈数+度数
    double total_angle = rounds * 360.0 + degrees;

    MOOSTrace("角度合成: %d圈 + %d度 -> %.1f°\n",
              rounds, degrees, total_angle);

    return total_angle;
}
*/

//---------------------------------------------------------
// Procedure: IsHighSpeedMode - 判断是否为高速模式
bool iElevator_HEU::IsHighSpeedMode()
{
    // 应急状态下速度判断失效，直接返回false
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("应急状态：速度判断失效，强制返回非高速模式\n");
        return false;
    }

    // 比较DESIRED_SPEED和配置的高速值
    bool is_high_speed = (m_dDesiredSpeed >= m_dHighSpeed);

    if (is_high_speed)
    {
        MOOSTrace("检测到高速模式：期望速度=%.1f >= 高速阈值=%.1f\n",
                  m_dDesiredSpeed, m_dHighSpeed);
    }
    return is_high_speed;
}
