//-----------------------------------------------
// iElevator_HEU 配置文件
// 升降电机控制系统配置
//-----------------------------------------------

ProcessConfig = iElevator_HEU
{
    AppTick   = 10    // 应用运行频率 10Hz
    CommsTick = 10    // MOOS通信频率 10Hz
    
    //------------------------------------------
    // 网络通信配置
    //------------------------------------------
    // 虚拟机IP地址，用于接收数据
    RecvIP = ************

    // 接收数据的端口号，对应CAN盒子的目标端口1
    RecvPort = 8001

    // CAN盒子的IP地址
    // 用于发送数据到CAN盒子
    DestIP = *************

    // 发送数据的目标端口号（CAN盒子的工作端口）
    DestPort = 4001
    
    //------------------------------------------
    // 电机控制参数
    //------------------------------------------
    // 升降速度配置（单位：mm/s）
    HIGH_SPEED = 2500.0           // 高速模式，2.5m/s = 2500mm/s

    // 电流检测配置（单位：安培）
    CURRENT_THRESHOLD = 8.0       // 卡住检测电流阈值，8.0A

    // 倒伏电机角度配置（单位：度）- 严格按照倒伏协议0°-90°范围
    MAX_ASCEND_ANGLE = 90      // 倒伏电机最大上升角度，协议限制0°-90°
    MIN_DESCEND_ANGLE = 0      // 倒伏电机最小下降角度，协议限制0°-90°

    // 注释：之前的升降电机配置已移除，倒伏电机严格遵循协议范围
    // 原升降配置：MAX_ASCEND_ANGLE = 570 (1圈+210度=570度) - 已废弃

    // 启动延迟配置
    STARTUP_DELAY = 60         // 程序启动后延迟60秒自动升起
    
    // 注释：原升降电机角度限制配置已废弃，倒伏电机使用上述0°-90°配置
    // MAX_ANGLE = 3240         // 原最大角度：10圈 - 已废弃
    // MIN_ANGLE = 360          // 原最小角度：0度 - 已废弃
    
    // 超时设置（单位：秒）
    TIMEOUT_SEC = 60.0          // 通信超时时间

    // 连接失败时的最大重试次数
    MaxRetries = 5

    // 重试之间的延迟时间（秒）
    RetryDelay = 2



}
