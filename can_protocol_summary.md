# 倒伏电机 CAN通讯协议

## 倒伏舵机CAN通讯协议

### 倒伏舵机CAN通讯参数

★ 波特率：250K。

★ 标识符：标准标识符（0x01~0x7FF）

★ 数据长度：4-8字节。

### 位置控制指令

**标识符**：当前控制 ID

**数据长度**：8 字节

**格式定义**：

| 字节 | 定义 | 释义 | 单位 | 备注 |
|------|------|------|------|------|
| 0 | 控制模式 | BB 为角度控制模式 | | 缺省BB |
| 1 | 备用 | | | 缺省为0 |
| 2 | 速度高位 | 运动时按照默认速度作业填充，静止时填充为0. | | 静止时缺省为0 |
| 3 | 速度低位 | | | |
| 4 | 期望度数高位 | 4000~0x7FFF对应0°到90° | 度 | 静止时，缺省为0 |
| 5 | 期望度数低位 | | | |
| 6 | 备用 | | | 缺省为0 |
| 7 | 备用 | | | 缺省为0 |

**（1）报文解释**

★ 控制模式：BB，表示此控制指令为角度控制指令。

★ 速度：位置环控制时为0。

★ 期望度数：4000-7FFF对应0°到90°。

**（2）报文示例**

控制舵机转到0度

| COBID(Hex) | 报文(Hex) |
|------------|-----------|
| 01 | BB 00 00 00 00 7F FF 00 |

控制舵机转到90度

| COBID(Hex) | 报文(Hex) |
|------------|-----------|
| 01 | BB 00 00 00 00 40 00 00 |

### 执行器反馈指令

电机上电后，自主1HZ发送状态报文，报文内容包含电机当前转速、当前圈数、末端角度、异常信息。

- **标识符**： 0x112
- **数据长度**：8 字节
- **格式定义**：

| 字节 | 定义 | 释义 | 单位 | 备注 |
|------|------|------|------|------|
| 0 | 控制模式 | BB 为角度控制模式 | | 缺省BB |
| 1 | 位置高位 | 0000~0x7FFF对应0°到90° | | 缺省为0 |
| 2 | 位置低位 | | | 静止时缺省为0 |
| 3 | 电流高位 | 以十六进制显示。 | | |
| 4 | 电流低位 | | | 静止时，缺省为0 |
| 5 | 电压高位 | 以十六进制显示。 | | |
| 6 | 电压低位 | | | 缺省为0 |
| 7 | 异常状态 | =0 无异常，=1过热 =2 过流 =3 电压过低 =4编码器错误 =5 堵转 =6 刹车电压过高 =7DRV驱动错误 | | 缺省为0 |

**（1）报文解释**

★ 控制模式：BB。

★ 位置：0000~0x7FFF对应0°到90°。

★ 电流：以十六进制显示。

★ 电压：以十六进制显示。

★ 电机错误信息：参考错误信息表。

**（2）报文示例**

例如发目标位置为90°：BB 00 00 00 7F FF 00 00

则返回：BB 7F FF 00 35 09 46 00

★ 角度反馈计算：7F FF对应90°

★ 电流反馈计算：00 35转换为十进制为53，53/100为0.53（单位A）

★ 电压反馈计算：09 46转换为十进制为2374，2374/100为23.74（单位V）

### 设置执行器ID号

**标识符**：0x700

主控发送数据长度：8字节

舵机反馈数据长度：8字节

| 舵机旧ID号 | 控制器下发 | 舵机新ID号 | 保留字节 |
|------------|------------|------------|----------|
| 0x00 | 0x**01** | 0x00 | 0x03 | 0x00 | 0x**XX** | 0x00 | 0x00 |

**设置成功**

舵机反馈

| 舵机新ID号 | 舵机反馈 | 保留字节 |
|------------|----------|----------|
| 0x00 | 0x**XX** | 0x01 | 0x03 | 0x00 | 0x00 | 0x00 | 0x00 |

### 设置执行器零位

**标识符**：0x700

主控发送数据长度：8字节

舵机反馈数据长度：8字节

| 舵机ID号 | 控制器下发 | 保留字节 |
|----------|------------|----------|
| 0x00 | 0x**01** | 0x00 | 0x04 | 0x00 | 0x00 | 0x00 | 0x00 |

**设置成功**

舵机反馈

| 舵机ID号 | 舵机反馈 | 保留字节 |
|----------|----------|----------|
| 0x00 | 0x01 | 0x01 | 0x04 | 0x00 | 0x00 | 0x00 | 0x00 |

